# 今日读 (Read Today)

一个极简的、基于 KEY 的“稍后读”服务。它的设计宗旨是简单、快速，并能在您的所有设备上提供便利。

本项目由一个核心网页应用 (PWA)、一个浏览器扩展和一个用于保存文章的小书签组成。

## 如何使用

### 1. 主应用 (PWA)

这里是您阅读已保存文章的地方。

1.  **打开应用:** 直接在浏览器中打开本项目下的 `index.html` 文件。
2.  **设置您的 KEY:** 首次打开时，系统会提示您创建一个个人 `KEY`。这是您用于同步所有设备的唯一密码。请选择一个便于记忆且足够安全的 KEY。
3.  **开始阅读:** 您保存的文章会显示在此页面上。点击任何文章都可以在新标签页中打开原文。使用“已读”按钮进行归档，或使用“删除”按钮永久移除。
4.  **(可选) 安装应用:** 在桌面和移动设备上，您的浏览器应该会在地址栏显示一个“安装”图标。安装后，“今日读”会像一个普通应用一样出现在您的主屏幕或应用启动器中，方便快速访问。

### 2. 浏览器扩展 (适用于 Chrome, Edge, Firefox 等)

这是在电脑上保存文章最便捷的方式。

1.  **加载扩展程序:**
    *   打开浏览器的扩展管理页面 (例如 `chrome://extensions`)。
    *   启用“开发者模式”。
    *   点击“加载已解压的扩展程序”，然后选择本项目下的 `extension` 文件夹 (位于 `/Users/<USER>/Workspaces/read-today/extension`)。
2.  **如何使用:**
    *   请确保您已在主应用中设置了您的 `KEY`。
    *   当您浏览到想要保存的页面时，只需点击浏览器工具栏上的“今日读”图标。图标会短暂地变为一个绿色的对勾，表示保存成功。

### 3. 小书签 (Bookmarklet)

这是一个无需安装任何程序的轻量级替代方案。

1.  **创建书签:**
    *   打开浏览器的书签管理器。
    *   创建一个新书签。
    *   在 **名称** 栏，输入 `保存到今日读`。
    *   在 **URL** 栏，将 `bookmarklet.js` 文件中的所有代码完整地复制并粘贴进去。
2.  **如何使用:**
    *   请确保您已在主应用中设置了您的 `KEY`。
    *   当您浏览到想要保存的页面时，只需点击收藏夹栏中的“保存到今日读”书签即可。

### 4. 移动端分享

从您手机上的任何应用（如微博、即刻、新闻客户端等）保存文章。

1.  **设置:** 首先，在您的手机浏览器中打开主应用，并将其“安装”或“添加到主屏幕”。
2.  **如何使用:**
    *   在任何应用中，找到您想保存的文章，然后点击系统原生的“分享”按钮。
    *   在弹出的应用列表中，找到并选择“今日读”。
    *   文章将会被自动保存。