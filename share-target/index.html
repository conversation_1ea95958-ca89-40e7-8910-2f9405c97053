<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Saving Article...</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: sans-serif; background-color: #f0f0f0; text-align: center; padding-top: 50px; }
        p { font-size: 1.2em; }
    </style>
</head>
<body>
    <p id="message">Saving article to your list...</p>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        const SUPABASE_URL = 'https://ptlxnbrhtgxspkrmyxkr.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0bHhuYnJodGd4c3Brcm15eGtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyMjA0MzIsImV4cCI6MjA2NDc5NjQzMn0.tvpOACqPS-GSbbNWiVweaIjolkU6U7u1AJKc6BXNM84';

        const { createClient } = supabase;
        const _supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        const messageEl = document.getElementById('message');

        async function handleShare() {
            const params = new URLSearchParams(window.location.search);
            let url = params.get('url');
            const text = params.get('text');
            let title = params.get('title');

            // If URL is not in the 'url' param, try to find it in the 'text' param.
            if (!url && text) {
                const urlRegex = /(https?:\/\/[^\s]+)/;
                const found = text.match(urlRegex);
                if (found) {
                    url = found[0];
                }
            }

            // If title is missing, use the text as title. If text is also missing, use a default.
            if (!title) {
                title = text || 'Untitled';
            }

            if (!url) {
                messageEl.textContent = 'Error: No URL provided.';
                return;
            }

            const userKey = localStorage.getItem('readToday_userKey');
            if (!userKey) {
                messageEl.textContent = 'Error: Please set your KEY in the main app first.';
                // Redirect to main app to set key
                setTimeout(() => window.location.href = '/', 2000);
                return;
            }

            const { error } = await _supabase.functions.invoke('save-article', {
                body: { article: { title, url, user_key: userKey } }
            });

            if (error) {
                console.error(error);
                messageEl.textContent = `Error: ${error.message}`;
            } else {
                messageEl.textContent = 'Article saved successfully!';
                // Close the window after a short delay
                setTimeout(() => window.close(), 1500);
            }
        }

        window.onload = handleShare;
    </script>
</body>
</html>
