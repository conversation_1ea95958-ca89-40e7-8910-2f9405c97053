# 今日读 (Read Today) - 产品与技术设计文档

## 1. 产品愿景

一个极简主义的“稍后读”服务，旨在解决用户发现内容但没有时间立即阅读的问题。其核心原则是：**简单、快速、跨平台便利**。

## 2. 核心功能

*   **保存链接:** 从任何设备快速保存文章 URL。
*   **阅读列表:** 一个干净、简洁的界面，用于查看和访问已保存的文章。
*   **归档:** 将文章标记为“已读”，将其移至归档，保持主列表清爽。
*   **删除数据:** 永久删除文章。

## 3. 用户系统: 基于 KEY 的认证机制

为了保持简单并避免传统的用户账户系统，本产品使用一个由用户定义的、唯一的 **KEY** 来进行认证和数据隔离。

*   **首次使用:** 系统会提示用户创建一个个人 `KEY`。这个 KEY 同时作为他的密码和身份标识。
*   **数据隔离:** 所有数据（保存的文章）都与此 `KEY` 绑定。一个用户只能访问与其特定 KEY 相关联的数据。
*   **本地存储:** 用户的 `KEY` 会被保存在浏览器的 `localStorage` 中以方便使用，这样在同一设备上就无需重复输入。

## 4. 产品组件

1.  **PWA (渐进式网络应用): 核心界面**
    *   阅读已保存文章的中心枢纽。
    *   显示所有未读文章的列表。
    *   允许用户打开、标记为已读或删除文章。
    *   可以被“安装”到手机主屏幕，提供类似原生应用的体验。
    *   处理初始的 `KEY` 设置流程。

2.  **小书签 (Bookmarklet) (PC/Mac)**
    *   一个由 JavaScript 驱动的浏览器书签。
    *   提供与扩展相同的一键保存功能，但无需安装任何程序。

5.  **Web Share Target (移动端)**
    *   集成在 PWA 中。
    *   允许“今日读”应用出现在手机原生的“分享”菜单中，从而实现从任何移动应用中保存内容。

## 5. 技术架构

*   **后端:** **Supabase**
    *   **数据库:** 云托管的 Postgres 数据库。
    *   **API:** 由 Supabase 自动生成的 RESTful API，无需编写后端代码。
    *   **认证:** 通过 Supabase 的行级安全 (RLS) 策略处理，基于 `user_key` 过滤数据。
*   **前端:** **原生 HTML, CSS, 和 JavaScript**
    *   保持应用轻量与快速。
    *   通过 Service Worker 和 Web App Manifest 实现 PWA 功能。
*   **数据库表结构 (`articles` 表):**
    *   `id` (uuid, 主键)
    *   `created_at` (timestamp, 默认 now())
    *   `title` (text)
    *   `url` (text, 唯一)
    *   `is_read` (boolean, 默认 false)
    *   `user_id` (text, 索引)