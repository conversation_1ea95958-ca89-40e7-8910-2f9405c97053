<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>今阅</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#ffffff"/>
</head>
<body>

    <header class="app-header">
        <div class="container header-container">
            <a href="/zh/" class="logo-link">
                <h1 class="logo">今阅</h1>
            </a>
            <!-- 未登录时显示的按钮 -->
            <div id="auth-header-actions" class="header-nav">
                <button id="lang-switcher-btn" title="切换语言"></button>
                <button id="theme-switcher" title="切换主题"></button>
            </div>
            <!-- 登录后显示的按钮 -->
            <div id="user-header-actions" class="header-nav hidden">
                <button id="theme-switcher-user" title="切换主题"></button>
                <button id="lang-switcher-btn-user" title="切换语言"></button>
                <button id="settings-btn">设置</button>
                <button id="logout-btn" class="logout-btn">退出</button>
            </div>
        </div>
    </header>

    <main id="app">
        <section id="auth-section" class="container">
            <div class="auth-content">
                <h2>欢迎使用 今阅</h2>
                <p>一个简洁、私密的稍后读工具。</p>
                <button id="login-btn">
                    <svg>...</svg>
                    <span>使用 Google 登录</span>
                </button>
            </div>
        </section>
        <section id="reading-list" class="container hidden">
            <ul id="articles-ul"></ul>
        </section>
    </main>

    <div id="loading" class="hidden"><div class="spinner"></div></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../js/branding.js" defer></script>
    <script src="../js/theme-switcher.js"></script>
    <script src="../js/lang-switcher.js"></script>
    <script src="../js/app.js"></script>
</body>
</html>