document.addEventListener('DOMContentLoaded', () => {
    const langSwitcherBtn = document.getElementById('lang-switcher-btn');
    if (langSwitcherBtn) {
        updateLangSwitcherButton();
        langSwitcherBtn.addEventListener('click', toggleLanguage);
    }

    function updateLangSwitcherButton() {
        const currentPath = window.location.pathname;
        const isZhPage = currentPath.startsWith('/zh/') || currentPath === '/zh';

        if (isZhPage) {
            langSwitcherBtn.textContent = 'EN';
            langSwitcherBtn.title = 'Switch to English';
        } else {
            langSwitcherBtn.textContent = '中';
            langSwitcherBtn.title = '切换到中文';
        }
    }

    function toggleLanguage() {
        const currentPath = window.location.pathname;
        let newPath;

        const isZhPage = currentPath.startsWith('/zh/') || currentPath === '/zh';

        if (isZhPage) {
            // Switch from Chinese to English
            if (currentPath === '/zh' || currentPath === '/zh/') {
                newPath = '/';
            } else {
                newPath = currentPath.replace('/zh/', '/');
            }
        } else {
            // Switch from English to Chinese
            if (currentPath === '/') {
                newPath = '/zh/';
            } else {
                newPath = '/zh' + currentPath;
            }
        }

        window.location.href = newPath;
    }
});