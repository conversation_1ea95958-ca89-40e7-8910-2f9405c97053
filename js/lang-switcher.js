document.addEventListener('DOMContentLoaded', () => {
    const langSwitcherBtn = document.getElementById('lang-switcher-btn');
    const langSwitcherBtnUser = document.getElementById('lang-switcher-btn-user');

    if (langSwitcherBtn) {
        updateLangSwitcherButton(langSwitcherBtn);
        langSwitcherBtn.addEventListener('click', toggleLanguage);
    }

    if (langSwitcherBtnUser) {
        updateLangSwitcherButton(langSwitcherBtnUser);
        langSwitcherBtnUser.addEventListener('click', toggleLanguage);
    }

    function updateLangSwitcherButton(button) {
        const currentPath = window.location.pathname;
        const isZhPage = currentPath.startsWith('/zh/') || currentPath === '/zh';

        if (isZhPage) {
            button.textContent = 'EN';
            button.title = 'Switch to English';
        } else {
            button.textContent = '中';
            button.title = '切换到中文';
        }
    }

    function toggleLanguage() {
        const currentPath = window.location.pathname;
        let newPath;

        const isZhPage = currentPath.startsWith('/zh/') || currentPath === '/zh';

        if (isZhPage) {
            // Switch from Chinese to English
            if (currentPath === '/zh' || currentPath === '/zh/') {
                newPath = '/';
            } else {
                newPath = currentPath.replace('/zh/', '/');
            }
        } else {
            // Switch from English to Chinese
            if (currentPath === '/') {
                newPath = '/zh/';
            } else {
                newPath = '/zh' + currentPath;
            }
        }

        window.location.href = newPath;
    }
});