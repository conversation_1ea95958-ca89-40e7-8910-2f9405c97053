const brandInfo = {
  en: {
    name: "Read Today",
    slogan: "Gather Morning Blooms at Dusk",
  },
  zh: {
    name: "今阅",
    slogan: "朝花夕拾",
  }
};

function getPageInfo(pathname) {
  const isZh = pathname.startsWith('/zh/');
  const lang = isZh ? 'zh' : 'en';
  const pageKey = pathname.replace(`/${lang}/`, '').replace('.html', '') || 'landing';

  const pageDetails = {
    'landing': {
      en: { title: 'Read Today - Save Articles for Later', description: 'A minimalist app to save articles and read them later across all your devices.', ogType: 'website' },
      zh: { title: '今阅 - 您的稍后读工具', description: '一款极简的稍后读应用，轻松保存文章，跨设备阅读。', ogType: 'website' }
    },
    'index': {
      en: { title: 'My Reading List - Read Today', description: 'Access your saved articles.', ogType: 'website' },
      zh: { title: '我的阅读列表 - 今阅', description: '访问您保存的文章。', ogType: 'website' }
    },
    'settings': {
      en: { title: 'Settings - Read Today', description: 'Manage your settings.', ogType: 'website' },
      zh: { title: '设置 - 今阅', description: '管理您的应用设置。', ogType: 'website' }
    },
    'privacy': {
        en: { title: 'Privacy Policy - Read Today', description: 'Our commitment to your privacy.', ogType: 'article' },
        zh: { title: '隐私政策 - 今阅', description: '我们对您隐私的承诺。', ogType: 'article' }
    },
    'terms': {
        en: { title: 'Terms of Service - Read Today', description: 'The terms and conditions for using our service.', ogType: 'article' },
        zh: { title: '服务条款 - 今阅', description: '使用我们服务的条款和条件。', ogType: 'article' }
    }
  };

  return {
    lang,
    brand: brandInfo[lang],
    page: pageDetails[pageKey] ? pageDetails[pageKey][lang] : pageDetails['landing'][lang],
  };
}

function updateBranding() {
  const { lang, brand, page } = getPageInfo(window.location.pathname);
  
  // Update title
  document.title = page.title;

  // Update meta description
  let descriptionTag = document.querySelector('meta[name="description"]');
  if (!descriptionTag) {
    descriptionTag = document.createElement('meta');
    descriptionTag.name = 'description';
    document.head.appendChild(descriptionTag);
  }
  descriptionTag.content = page.description;

  // Update slogan in the body
  const sloganEl = document.querySelector('.slogan');
  if (sloganEl) {
    sloganEl.textContent = brand.slogan;
  }
  
  // Set lang attribute on <html>
  document.documentElement.lang = lang;

  // Update Open Graph and Twitter Card meta tags
  const ogTitle = page.title;
  const ogDescription = page.description;
  const ogUrl = window.location.href;
  const ogImage = window.location.origin + '/icons/icon-512x512.png'; // Assuming a default image
  const ogType = page.ogType || 'website';

  const metaTags = [
    { property: 'og:title', content: ogTitle },
    { property: 'og:description', content: ogDescription },
    { property: 'og:url', content: ogUrl },
    { property: 'og:image', content: ogImage },
    { property: 'og:type', content: ogType },
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: ogTitle },
    { name: 'twitter:description', content: ogDescription },
    { name: 'twitter:image', content: ogImage },
  ];

  metaTags.forEach(tagData => {
    let tag = document.querySelector(`meta[${tagData.property ? 'property' : 'name'}="${tagData.property || tagData.name}"]`);
    if (!tag) {
      tag = document.createElement('meta');
      if (tagData.property) tag.setAttribute('property', tagData.property);
      if (tagData.name) tag.setAttribute('name', tagData.name);
      document.head.appendChild(tag);
    }
    tag.content = tagData.content;
  });

  // Add JSON-LD Structured Data (Example for a WebSite)
  const jsonLdScript = document.createElement('script');
  jsonLdScript.type = 'application/ld+json';
  jsonLdScript.textContent = JSON.stringify({
    "@context": "http://schema.org",
    "@type": page.ogType === 'article' ? 'Article' : 'WebSite',
    "name": brand.name,
    "url": window.location.origin,
    "description": page.description,
    "publisher": {
      "@type": "Organization",
      "name": brand.name,
      "logo": {
        "@type": "ImageObject",
        "url": window.location.origin + '/icons/icon-192x192.png'
      }
    }
  });
  document.head.appendChild(jsonLdScript);
}

// Run on page load
document.addEventListener('DOMContentLoaded', updateBranding);
