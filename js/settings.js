// Initialize Supabase
const SUPABASE_URL = 'https://ptlxnbrhtgxspkrmyxkr.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0bHhuYnJodGd4c3Brcm15eGtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyMjA0MzIsImV4cCI6MjA2NDc5NjQzMn0.tvpOACqPS-GSbbNWiVweaIjolkU6U7u1AJKc6BXNM84';

const { createClient } = supabase;
const _supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

document.addEventListener('DOMContentLoaded', () => {
    const bookmarkletLink = document.getElementById('bookmarklet-link');
    const logoutBtn = document.getElementById('logout-btn');

    // Setup bookmarklet
    const bookmarkletPath = window.location.pathname.startsWith('/zh/') ? '../js/bookmarklet.js' : 'js/bookmarklet.js';
    fetch(bookmarkletPath)
        .then(response => response.text())
        .then(code => { bookmarkletLink.href = code; })
        .catch(console.error);

    // Setup logout functionality
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async () => {
            const isZh = window.location.pathname.startsWith('/zh/');
            const confirmMessage = isZh ? '您确定要退出登录吗？' : 'Are you sure you want to logout?';

            if (confirm(confirmMessage)) {
                try {
                    const { error } = await _supabase.auth.signOut();
                    if (error) {
                        const errorMessage = isZh ? `退出失败: ${error.message}` : `Logout failed: ${error.message}`;
                        alert(errorMessage);
                    } else {
                        // Redirect to home page
                        window.location.href = isZh ? '/zh/' : '/';
                    }
                } catch (err) {
                    const errorMessage = isZh ? `退出失败: ${err.message}` : `Logout failed: ${err.message}`;
                    alert(errorMessage);
                }
            }
        });
    }
});
