// Supabase Client
const SUPABASE_URL = 'https://ptlxnbrhtgxspkrmyxkr.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0bHhuYnJodGd4c3Brcm15eGtyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkyMjA0MzIsImV4cCI6MjA2NDc5NjQzMn0.tvpOACqPS-GSbbNWiVweaIjolkU6U7u1AJKc6BXNM84';

const { createClient } = supabase;
const _supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// DOM Elements
const authSection = document.getElementById('auth-section');
const loginBtn = document.getElementById('login-btn');
const readingListSection = document.getElementById('reading-list');
const articlesUl = document.getElementById('articles-ul');
const loadingIndicator = document.getElementById('loading');
const authHeaderActions = document.getElementById('auth-header-actions');
const userHeaderActions = document.getElementById('user-header-actions');
const pasteBtn = document.getElementById('paste-btn');
const settingsBtn = document.getElementById('settings-btn');
const logoutBtn = document.getElementById('logout-btn');

// App State
let isDataLoaded = false; // Flag to prevent duplicate data loading

// --- Utility Functions ---
function showToast(message, isError = false) {
    const toastId = 'read-today-toast';
    const existingToast = document.getElementById(toastId);
    if (existingToast) existingToast.remove();
    const toast = document.createElement('div');
    toast.id = toastId; toast.textContent = message;
    Object.assign(toast.style, { position: 'fixed', top: '20px', right: '20px', padding: '12px 20px', backgroundColor: isError ? '#d9534f' : '#5cb85c', color: 'white', borderRadius: '8px', zIndex: '9999', fontSize: '16px', boxShadow: '0 4px 15px rgba(0,0,0,0.2)', transition: 'opacity 0.3s, transform 0.3s ease-out', opacity: '0', transform: 'translateY(-20px)' });
    document.body.appendChild(toast);
    setTimeout(() => { toast.style.opacity = '1'; toast.style.transform = 'translateY(0)'; }, 10);
    setTimeout(() => { toast.style.opacity = '0'; toast.style.transform = 'translateY(-20px)'; setTimeout(() => toast.remove(), 300); }, 3500);
}

function getDomain(url) {
    try { return new URL(url).hostname.replace(/^www\./, ''); } catch (e) { return ''; }
}

function formatTimestamp(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    if (isToday) return `${hours}:${minutes}`;
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${month}-${day} ${hours}:${minutes}`;
}

// --- Data Functions ---
async function fetchArticles(forceRefresh = false) {
    // Prevent duplicate loading unless forced
    if (isDataLoaded && !forceRefresh) {
        return;
    }

    loadingIndicator.classList.remove('hidden');
    articlesUl.innerHTML = ''; // Clear existing articles to prevent duplicates

    try {
        const { data, error } = await _supabase.from('articles').select('*').order('created_at', { ascending: false });

        if (error) {
            showToast(`获取文章失败: ${error.message}`, true);
            return;
        }

        if (data.length === 0) {
            // Determine language based on current path
            const isZhPage = window.location.pathname.startsWith('/zh');
            const emptyTitle = isZhPage ? '列表为空' : 'List is empty';
            const emptySubtitle = isZhPage ? '您的阅读列表是空的，请使用扩展或书签添加文章。' : 'Use the extension or bookmarklet to add articles.';

            articlesUl.innerHTML = `<div class="empty-state"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 010 3.75H5.625a1.875 1.875 0 010-3.75z" /></svg><h3>${emptyTitle}</h3><p>${emptySubtitle}</p></div>`;
        } else {
            data.forEach(renderArticle);
        }

        isDataLoaded = true; // Mark data as loaded
    } catch (err) {
        showToast(`网络错误: ${err.message}`, true);
    } finally {
        loadingIndicator.classList.add('hidden');
    }
}

// Manual refresh function
async function manualRefresh() {
    showToast('正在刷新数据...');
    await fetchArticles(true); // Force refresh
    showToast('数据已更新！');
}

async function saveArticle(articleData) {
    const { data: { user } } = await _supabase.auth.getUser();
    if (!user) { showToast('请先登录', true); return; }
    const { error } = await _supabase.from('articles').insert({ ...articleData, user_id: user.id });
    if (error) { showToast(`保存失败: ${error.message}`, true); }
    else { showToast('保存成功!'); fetchArticles(true); } // Force refresh after saving
}

async function markAsRead(id) {
    const { error } = await _supabase.from('articles').update({ is_read: true }).eq('id', id);
    if (error) { showToast(`操作失败: ${error.message}`, true); } 
    else { document.querySelector(`li[data-id="${id}"]`)?.remove(); }
}

async function deleteArticle(id) {
    const { error } = await _supabase.from('articles').delete().eq('id', id);
    if (error) { showToast(`删除失败: ${error.message}`, true); } 
    else { document.querySelector(`li[data-id="${id}"]`)?.remove(); }
}

// --- UI Rendering ---
function renderArticle(article) {
    const li = document.createElement('li');
    li.dataset.id = article.id;
    const favicon = document.createElement('img');
    favicon.className = 'article-favicon';
    favicon.src = `https://www.google.com/s2/favicons?domain=${getDomain(article.url)}&sz=32`;
    favicon.onerror = () => { favicon.style.display = 'none'; };
    const contentDiv = document.createElement('div');
    contentDiv.className = 'article-content';
    const a = document.createElement('a');
    a.href = article.url; a.target = '_blank'; a.rel = 'noopener noreferrer'; a.className = 'article-title';
    const titleText = document.createElement('span');
    titleText.className = 'title-text';
    titleText.textContent = article.title || article.url;
    const domainText = document.createElement('span');
    domainText.className = 'domain-text';
    domainText.textContent = `${formatTimestamp(article.created_at)} · ${getDomain(article.url)}`;
    a.appendChild(titleText); a.appendChild(domainText);
    contentDiv.appendChild(a);
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'article-actions';
    const readBtn = document.createElement('button');
    readBtn.title = '已读';
    readBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="20" height="20"><path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.052-.143Z" clip-rule="evenodd" /></svg>`;
    readBtn.onclick = () => markAsRead(article.id);
    const deleteBtn = document.createElement('button');
    deleteBtn.title = '删除';
    deleteBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="20" height="20"><path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 0 0 6 3.75v.443c-.795.077-1.58.22-2.365.468a.75.75 0 1 0 .23 1.482l.149-.022.841 10.518A2.75 2.75 0 0 0 7.596 19h4.807a2.75 2.75 0 0 0 2.742-2.53l.841-10.52.149.023a.75.75 0 0 0 .23-1.482A41.03 41.03 0 0 0 14 4.193v-.443A2.75 2.75 0 0 0 11.25 1h-2.5ZM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4ZM8.58 7.72a.75.75 0 0 0-1.5.06l.3 7.5a.75.75 0 1 0 1.5-.06l-.3-7.5Zm4.34.06a.75.75 0 1 0-1.5-.06l-.3 7.5a.75.75 0 1 0 1.5.06l.3-7.5Z" clip-rule="evenodd" /></svg>`;
    deleteBtn.onclick = () => deleteArticle(article.id);
    actionsDiv.appendChild(readBtn); actionsDiv.appendChild(deleteBtn);
    li.appendChild(favicon); li.appendChild(contentDiv); li.appendChild(actionsDiv);
    articlesUl.appendChild(li);
}

// --- App Flow & State ---
function showLoginScreen() {
    authSection.classList.remove('hidden');
    readingListSection.classList.add('hidden');
    authHeaderActions.classList.remove('hidden');
    userHeaderActions.classList.add('hidden');
    // Reset data loaded flag when showing login screen
    isDataLoaded = false;
}

function showAppScreen() {
    authSection.classList.add('hidden');
    readingListSection.classList.remove('hidden');
    authHeaderActions.classList.add('hidden');
    userHeaderActions.classList.remove('hidden');
    // Only fetch articles if not already loaded
    fetchArticles();
}

async function handleGoogleLogin() {
    const { error } = await _supabase.auth.signInWithOAuth({ provider: 'google' });
    if (error) showToast(`登录失败: ${error.message}`, true);
}

async function handleLogout() {
    if (confirm('您确定要退出登录吗？')) {
        const { error } = await _supabase.auth.signOut();
        if (error) {
            showToast(`退出失败: ${error.message}`, true);
        } else {
            // Reset data loaded flag on logout
            isDataLoaded = false;
        }
    }
}

async function saveFromClipboard() {
    try {
        const text = await navigator.clipboard.readText();
        const found = text.match(/(https?:\/\/[^\s]+)/);
        if (found && found[0]) {
            const url = found[0];
            showToast('正在获取标题...');
            const { data: titleData, error: titleError } = await _supabase.functions.invoke('get-url-title', { body: { url } });
            const title = titleError ? url : titleData.title || url;
            await saveArticle({ title, url });
        } else { showToast('剪贴板中未找到有效链接', true); }
    } catch (err) { showToast(`无法读取剪贴板: ${err.message}`, true); }
}

// --- App Initialization ---
async function initializeApp() {
    // Set up event listeners
    loginBtn.addEventListener('click', handleGoogleLogin);
    logoutBtn.addEventListener('click', handleLogout);
    pasteBtn.addEventListener('click', saveFromClipboard);
    if (settingsBtn) {
        settingsBtn.addEventListener('click', () => {
            const isZh = window.location.pathname.startsWith('/zh');
            window.location.href = isZh ? '/zh/settings.html' : '/settings.html';
        });
    }
    document.getElementById('refresh-btn').addEventListener('click', manualRefresh);
    document.addEventListener('keydown', (e) => { if ((e.metaKey || e.ctrlKey) && e.key === 'v') { e.preventDefault(); saveFromClipboard(); } });

    // Listen for auth state changes
    _supabase.auth.onAuthStateChange((event, session) => {
        // Only handle specific auth events to avoid duplicate calls
        if (event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
            if (session) {
                showAppScreen();
            } else {
                showLoginScreen();
            }
        }
    });

    // Initial session check
    const { data: { session } } = await _supabase.auth.getSession();
    if (session) {
        showAppScreen();
    } else {
        showLoginScreen();
    }
}

document.addEventListener('DOMContentLoaded', initializeApp);

// Register Service Worker
// if ('serviceWorker' in navigator) {
//   window.addEventListener('load', () => {
//     navigator.serviceWorker.register('/service-worker.js').then(reg => console.log('SW registered.'), err => console.error('SW registration failed:', err));
//   });
// }