<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Read Today</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>

    <header class="app-header">
        <div class="container header-container">
            <a href="/" class="logo-link">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
                    <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 0 1-.02 1.06L8.832 10l3.938 3.71a.75.75 0 1 1-1.04 1.08l-4.5-4.25a.75.75 0 0 1 0-1.08l4.5-4.25a.75.75 0 0 1 1.06.02Z" clip-rule="evenodd" />
                </svg>
                <span>Back to Home</span>
            </a>
            <div class="header-nav">
                <button id="lang-switcher-btn" title="Switch language"></button>
                <button id="theme-switcher" title="Switch theme"></button>
            </div>
        </div>
    </header>

    <main id="app">
        <div class="container">
            <h2 class="page-title">Settings</h2>

            <div class="setting-card">
                <h3>Bookmarklet for Easy Saving</h3>
                <p class="setting-description">Drag the blue pill below to your browser's bookmarks bar to save pages with one click.</p>
                <a id="bookmarklet-link" href="#" class="bookmarklet">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="16" height="16"><path d="M3 3.5A1.5 1.5 0 0 1 4.5 2h5.25a.75.75 0 0 1 0 1.5H4.5a.75.75 0 0 0-.75.75v10.5a.75.75 0 0 0 .75.75h10.5a.75.75 0 0 0 .75-.75V10.5a.75.75 0 0 1 1.5 0v5.25A1.5 1.5 0 0 1 15.5 17H4.5A1.5 1.5 0 0 1 3 15.5v-12Z" /><path d="M10.75 2.75a.75.75 0 0 0 0 1.5h2.539l-6.22 6.22a.75.75 0 1 0 1.06 1.06l6.22-6.22v2.539a.75.75 0 0 0 1.5 0V3.5a.75.75 0 0 0-.75-.75h-4.25Z" /></svg>
                    <span>Save to Read Today</span>
                </a>
            </div>
        </div>
    </main>

    <script src="js/theme-switcher.js"></script>
    <script src="js/lang-switcher.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
