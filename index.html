<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Read Today</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#ffffff"/>
</head>
<body>

    <header class="app-header">
        <div class="container header-container">
            <a href="/" class="logo-link">
                <h1 class="logo">Read Today</h1>
            </a>
            <!-- 未登录时显示的按钮 -->
            <div id="auth-header-actions" class="header-nav">
                <button id="lang-switcher-btn" title="Switch language"></button>
                <button id="theme-switcher" title="Switch theme"></button>
            </div>
            <!-- 登录后显示的按钮 -->
            <div id="user-header-actions" class="header-nav hidden">
                <button id="theme-switcher-user" title="Switch theme"></button>
                <button id="lang-switcher-btn-user" title="Switch language"></button>
                <button id="settings-btn">Settings</button>
                <button id="logout-btn" class="logout-btn">Logout</button>
            </div>
        </div>
    </header>

    <main id="app">
        <section id="auth-section" class="container">
            <div class="auth-content">
                <h2>Welcome to Read Today</h2>
                <p>A simple and private read-later tool.</p>
                <button id="login-btn">
                    <svg>...</svg>
                    <span>Sign in with Google</span>
                </button>
            </div>
        </section>
        <section id="reading-list" class="container hidden">
            <div class="list-header">
                <h2 class="list-title">My Reading List</h2>
                <div class="list-actions">
                    <button id="refresh-btn" title="Refresh data" class="action-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18">
                            <path fill-rule="evenodd" d="M15.312 11.424a5.5 5.5 0 1 1-9.201-2.527m-.342.342a.75.75 0 0 0 1.061 1.061l2.414-2.414a.25.25 0 0 0-.06-.353l-2.414-2.414a.75.75 0 0 0-1.061 1.061l1.707 1.707H4.5A.75.75 0 0 0 3.75 10a.75.75 0 0 0 .75.75h4.5a.75.75 0 0 0 .75-.75V5.5a.75.75 0 0 0-1.5 0v3.793l-1.707-1.707Z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <button id="paste-btn" title="Paste URL" class="action-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18">
                            <path fill-rule="evenodd" d="M8 1a2.5 2.5 0 0 0-2.5 2.5V4h-.5A2.5 2.5 0 0 0 2.5 6.5v8A2.5 2.5 0 0 0 5 17h10a2.5 2.5 0 0 0 2.5-2.5v-8A2.5 2.5 0 0 0 15 4h-.5v-.5A2.5 2.5 0 0 0 12 1H8ZM12 4H8v-.5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1V4Z" clip-rule="evenodd" />
                        </svg>
                        <span>Paste</span>
                    </button>
                </div>
            </div>
            <ul id="articles-ul"></ul>
        </section>
    </main>

    <div id="loading" class="hidden"><div class="spinner"></div></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/branding.js" defer></script>
    <script src="js/theme-switcher.js"></script>
    <script src="js/lang-switcher.js"></script>
    <script src="js/app.js"></script>
</body>
</html>