<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Read Today</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#ffffff"/>
</head>
<body>

    <header class="app-header">
        <div class="container header-container">
            <h1 class="logo">Read Today</h1>
            <div id="header-actions" class="header-actions hidden">
                <button id="refresh-btn" title="Refresh data">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
                        <path fill-rule="evenodd" d="M15.312 11.424a5.5 5.5 0 1 1-9.201-2.527m-.342.342a.75.75 0 0 0 1.061 1.061l2.414-2.414a.25.25 0 0 0-.06-.353l-2.414-2.414a.75.75 0 0 0-1.061 1.061l1.707 1.707H4.5A.75.75 0 0 0 3.75 10a.75.75 0 0 0 .75.75h4.5a.75.75 0 0 0 .75-.75V5.5a.75.75 0 0 0-1.5 0v3.793l-1.707-1.707Z" clip-rule="evenodd" />
                    </svg>
                </button>
                <button id="lang-switcher-btn" title="Switch language"></button>
                <button id="theme-switcher" title="Switch theme"></button>
                <button id="paste-btn">Paste</button>
                <button id="settings-btn">Settings</button>
                <button id="logout-btn">Logout</button>
            </div>
        </div>
    </header>

    <main id="app">
        <section id="auth-section" class="container">
            <div class="auth-content">
                <h2>Welcome to Read Today</h2>
                <p>A simple and private read-later tool.</p>
                <button id="login-btn">
                    <svg>...</svg>
                    <span>Sign in with Google</span>
                </button>
            </div>
        </section>
        <section id="reading-list" class="container hidden">
            <ul id="articles-ul"></ul>
        </section>
    </main>

    <div id="loading" class="hidden"><div class="spinner"></div></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/branding.js" defer></script>
    <script src="js/theme-switcher.js"></script>
    <script src="js/lang-switcher.js"></script>
    <script src="js/app.js"></script>
</body>
</html>