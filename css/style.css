@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
    --primary-color: #007aff;
    --primary-hover: #005ecb;
    --background-color: #f4f6f8;
    --card-background: #ffffff;
    --text-color: #212529;
    --subtle-text: #6c757d;
    --border-color: #e5e5ea;
}

body.dark-mode {
    --primary-color: #0a84ff;
    --primary-hover: #0077f2;
    --background-color: #000000;
    --card-background: #1c1c1e;
    --text-color: #ffffff;
    --subtle-text: #8d8d93;
    --border-color: #3a3a3c;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    line-height: 1.6;
}

.container {
    max-width: 750px;
    margin: 1.5rem auto;
    padding: 2rem 2.5rem;
}

/* Header */
.app-header {
    background-color: var(--card-background);
    border-bottom: 1px solid var(--border-color);
    padding: 0 1rem;
}
.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 1rem 0;
}
.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}
.header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Buttons */
button {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 18px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.1s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}
button:hover { background-color: var(--primary-hover); }
button:active { transform: scale(0.97); }

.header-actions button {
    background-color: #f0f0f7;
    color: var(--primary-color);
    font-weight: 600;
}
.header-actions button:hover { background-color: #e5e5ea; }

.header-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#theme-switcher {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--subtle-text);
    padding: 4px;
}

#theme-switcher:hover {
    color: var(--text-color);
}

#theme-switcher svg {
    width: 24px;
    height: 24px;
}

.shortcut-hint {
    font-size: 0.8rem;
    color: var(--subtle-text);
    margin-right: 1rem;
}

/* Key Setup */
#key-setup-content {
    text-align: center;
    padding: 2rem 0;
}
#key-input {
    width: 80%;
    padding: 14px;
    margin: 1.5rem 0;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}
#key-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0,123,255,.15);
}

/* Article List */
#articles-ul {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 4px 25px rgba(0,0,0,0.05);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

#articles-ul li {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s;
}
#articles-ul li:last-child { border-bottom: none; }
#articles-ul li:hover { background-color: #f8f9fa; }

.article-favicon {
    width: 16px;
    height: 16px;
    margin-right: 1rem;
    flex-shrink: 0;
}

.article-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
}

.article-title {
    text-decoration: none;
}

.article-title {
    display: block; /* Or flex, depending on desired alignment */
}

.article-title .title-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

.article-title .domain-text {
    font-size: 0.8rem;
    color: var(--subtle-text);
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.article-title:hover .title-text { color: var(--primary-color); }

.article-actions {
    display: flex;
    gap: 0.5rem;
}
.article-actions button {
    background: none;
    border: 1px solid transparent;
    color: var(--subtle-text);
    padding: 6px;
    opacity: 0.6;
    transition: opacity 0.2s, background-color 0.2s;
}
.article-actions button:hover { opacity: 1; background-color: #e9ecef; }

/* Empty State */
.empty-state { text-align: center; padding: 3rem 1rem; color: var(--subtle-text); }
.empty-state svg { width: 50px; height: 50px; margin-bottom: 1rem; opacity: 0.4; }

/* Settings Page */
.logo-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--subtle-text);
    font-weight: 500;
    transition: color 0.2s;
}
.logo-link:hover { color: var(--text-color); }

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.setting-card {
    background-color: var(--card-background);
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.setting-card h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.setting-card .setting-description {
    font-size: 0.9rem;
    color: var(--subtle-text);
    margin-top: 0;
    margin-bottom: 1.5rem;
}

.key-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f5f5f7;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.key-display strong {
    font-family: monospace;
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
}

.input-group {
    display: flex;
    gap: 0.75rem;
}

.input-group input {
    flex-grow: 1;
    margin: 0 !important;
    border-radius: 8px;
    border: 1px solid #d1d1d6;
    padding: 12px;
}

.input-group button {
    white-space: nowrap;
}

.bookmarklet {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--primary-color);
    color: white;
    padding: 12px 20px;
    border-radius: 25px; /* Pill shape */
    text-decoration: none;
    font-weight: 500;
    cursor: move;
    transition: background-color 0.2s, transform 0.1s;
}

.bookmarklet:hover { 
    background-color: var(--primary-hover); 
}

.bookmarklet:active {
    transform: scale(0.97);
}

/* Loading Spinner */
.spinner { border: 4px solid rgba(0, 0, 0, 0.1); width: 36px; height: 36px; border-radius: 50%; border-left-color: var(--primary-color); animation: spin 1s ease infinite; }
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
#loading { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); }

/* Utility */
.hidden { display: none !important; }

/* Responsive adjustments for mobile */
@media (max-width: 600px) {
    .container {
        margin: 1rem auto;
        padding: 1rem;
    }

    .header-container {
        padding: 0.5rem 0;
    }

    .logo {
        font-size: 1.25rem;
    }

    .header-actions button {
        padding: 6px 12px;
        font-size: 0.85rem;
    }

    #articles-ul li {
        padding: 0.75rem 1rem;
    }

    .setting-card {
        padding: 1.5rem;
    }

    .page-title {
        font-size: 1.5rem;
    }
}
