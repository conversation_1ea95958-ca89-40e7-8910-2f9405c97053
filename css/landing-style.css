@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;800&display=swap');

:root {
    --primary-color: #007aff;
    --primary-hover: #005ecb;
    --background-color: #ffffff;
    --section-bg-color: #f9f9fb;
    --text-color: #1d1d1f;
    --subtle-text: #515154;
}

body.dark-mode {
    --primary-color: #0a84ff;
    --primary-hover: #0077f2;
    --background-color: #000000;
    --section-bg-color: #1c1c1e;
    --text-color: #f5f5f7;
    --subtle-text: #8d8d93;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    line-height: 1.7;
    -webkit-font-smoothing: antialiased;
}

.container {
    max-width: 1080px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Header */
.main-header {
    padding: 1.5rem 0;
    border-bottom: 1px solid #e5e5ea;
}
.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.logo { font-size: 1.5rem; font-weight: 700; }

/* Buttons */
.cta-button-small, .cta-button-large {
    display: inline-block;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.1s;
}
.cta-button-small {
    background-color: #f0f0f7;
    color: var(--primary-color);
    padding: 8px 16px;
    font-size: 0.9rem;
}
.cta-button-small:hover { background-color: #e5e5ea; }
.cta-button-large {
    background-color: var(--primary-color);
    color: white;
    padding: 16px 32px;
    font-size: 1.1rem;
    margin-top: 2rem;
}
.cta-button-large:hover { background-color: var(--primary-hover); }
.cta-button-large:active { transform: scale(0.98); }

/* Hero Section */
.hero {
    text-align: center;
    padding: 6rem 0;
}
.hero h1 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin: 0 0 1rem;
}
.hero .subtitle {
    font-size: 1.25rem;
    color: var(--subtle-text);
    max-width: 650px;
    margin: 0 auto;
}

/* How It Works Section */
.how-it-works {
    background-color: var(--section-bg-color);
    padding: 5rem 0;
    text-align: center;
}
.how-it-works h2 { font-size: 2.5rem; margin-top: 0; }
.steps-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 4rem;
}
.step .step-icon { font-size: 2.5rem; }
.step h3 { font-size: 1.25rem; margin: 1rem 0 0.5rem; }
.step p { color: var(--subtle-text); }

/* Features Section */
.features {
    padding: 6rem 0;
}
.feature-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}
.feature-text h2 { font-size: 2.5rem; margin-top: 0; }
.feature-text p { font-size: 1.1rem; color: var(--subtle-text); }
.feature-text ul { list-style: none; padding: 0; }
.feature-text ul li { font-weight: 500; margin-bottom: 0.5rem; }
.feature-image img { max-width: 100%; border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }

/* Philosophy Section */
.philosophy {
    background-color: var(--section-bg-color);
    padding: 5rem 0;
    text-align: center;
}
.philosophy h2 { font-size: 2.5rem; margin-top: 0; }
.philosophy p {
    font-size: 1.1rem;
    color: var(--subtle-text);
    max-width: 700px;
    margin: 1rem auto;
}

/* Final CTA Section */
.final-cta {
    padding: 6rem 0;
    text-align: center;
}
.final-cta h2 { font-size: 2.5rem; margin-top: 0; max-width: 600px; margin-left: auto; margin-right: auto; }

/* Footer */
.main-footer {
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid #e5e5ea;
}
#footer-links {
    margin-bottom: 1rem;
}
#footer-links a {
    color: var(--subtle-text);
    text-decoration: none;
    margin: 0 0.75rem;
    font-size: 0.9rem;
}
#footer-links a:hover {
    text-decoration: underline;
}
.main-footer p { color: var(--subtle-text); font-size: 0.9rem; }

/* Responsive */
@media (max-width: 768px) {
    .steps-grid, .feature-item {
        grid-template-columns: 1fr;
    }
    .hero h1 { font-size: 2.5rem; }
    .how-it-works h2, .feature-text h2, .philosophy h2, .final-cta h2 { font-size: 2rem; }
}
