# 今日读 (Read Today) - 待办事项清单

## 第一阶段: 环境设置与核心后端

- [x] 在 Supabase 上建立新项目。
- [x] 在 Supabase 数据库中根据指定的结构创建 `articles` 表。
- [x] 配置行级安全 (RLS) 策略，以确保基于 `user_key` 的数据隔离。

## 第二阶段: 核心前端 (PWA)

- [x] 创建 PWA 的基础 HTML 结构 (`index.html`)。
- [x] 使用 CSS 设计阅读列表的样式 (`style.css`)。
- [x] 在 JavaScript 中编写主要应用逻辑 (`app.js`)。
- [x] 实现“输入/设置您的 KEY”流程，并将 KEY 保存到 `localStorage`。
- [x] 实现从 Supabase 获取并显示文章列表的功能。
- [x] 实现“标记为已读”功能。
- [x] 实现“删除”功能。
- [x] 创建用于 PWA 安装的 `manifest.json` 文件。
- [x] 创建 `service-worker.js` 以实现基本的离线功能。

## 第三阶段: 保存机制

- [x] 开发浏览器扩展 (清单、背景脚本、弹出窗口)。
- [x] 创建小书签的 JavaScript 代码。
- [x] 在 PWA 清单中实现 Web Share Target API。

## 第四阶段: 完成与文档

- [x] 编写 `README.md` 文件，包含如何使用应用、扩展和小书签的说明。
- [ ] (可选) 在不同浏览器和设备上测试所有功能。
- [x] (可选) 将 PWA 部署到静态托管服务 (如 Vercel 或 Netlify)。